{"name": "grumble-backend", "version": "1.0.0", "description": "Grumble Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["grumble", "backend", "api", "express"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "express-session": "^1.18.1", "mongoose": "^8.15.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}}