// Configuration
const API_BASE_URL = 'http://localhost:3000/api';

// Check authentication and load user data
async function checkAuth() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            populateUserData(data.user);
        } else {
            // User not authenticated, redirect to login
            window.location.href = 'login.html';
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        window.location.href = 'login.html';
    }
}

// Populate user data in the dashboard
function populateUserData(user) {
    // Update user name
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = user.name || 'İstifadəçi';
    }
    
    // Update user email
    const userEmailElement = document.getElementById('user-email');
    if (userEmailElement) {
        userEmailElement.textContent = user.email || 'N/A';
    }
    
    // Update user avatar
    const userAvatarElement = document.getElementById('user-avatar');
    if (userAvatarElement && user.avatar) {
        userAvatarElement.innerHTML = `
            <img src="${user.avatar}" alt="Profile" class="rounded-circle" width="80" height="80">
        `;
    }
    
    // Update provider info
    const providerInfoElement = document.getElementById('provider-info');
    if (providerInfoElement) {
        if (user.provider === 'google') {
            providerInfoElement.innerHTML = `
                <i class="fab fa-google text-danger me-1"></i>
                Google hesabı ilə daxil oldunuz
            `;
        } else {
            providerInfoElement.innerHTML = `
                <i class="fas fa-envelope me-1"></i>
                Email hesabı ilə daxil oldunuz
            `;
        }
    }
}

// Logout function
async function logout() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/logout`, {
            method: 'POST',
            credentials: 'include'
        });
        
        if (response.ok) {
            window.location.href = 'login.html';
        } else {
            alert('Çıxış zamanı xəta baş verdi');
        }
    } catch (error) {
        console.error('Logout failed:', error);
        alert('Çıxış zamanı xəta baş verdi');
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuth();
    
    // Setup logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
});
