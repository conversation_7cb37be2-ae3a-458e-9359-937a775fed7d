const express = require('express');
const router = express.Router();

// Categories data (in a real app, this would come from a database)
const categories = [
  {
    "title": "E-ticarət",
    "description": "<PERSON>layn alış-veriş",
    "icon": "🛒"
  },
  {
    "title": "Yemək Çatdırılması",
    "description": "Restoran və kafelər",
    "icon": "🍕"
  },
  {
    "title": "Nəqliyyat",
    "description": "Taksi və avtobus",
    "icon": "🚗"
  },
  {
    "title": "Kommunal",
    "description": "Su, qaz, elektrik",
    "icon": "⚡"
  }
];

// GET: Get all categories
router.get('/', (req, res) => {
  try {
    res.json(categories);
  } catch (error) {
    console.error('Categories fetch error:', error);
    res.status(500).json({ error: 'Kateqoriyalar yüklənərkən xəta baş verdi' });
  }
});

// GET: Get category by ID
router.get('/:id', (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    if (categoryId >= 0 && categoryId < categories.length) {
      res.json(categories[categoryId]);
    } else {
      res.status(404).json({ error: 'Kateqoriya tapılmadı' });
    }
  } catch (error) {
    console.error('Category fetch error:', error);
    res.status(500).json({ error: 'Kateqoriya yüklənərkən xəta baş verdi' });
  }
});

module.exports = router;
