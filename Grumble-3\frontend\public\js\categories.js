// Categories page JavaScript
const API_BASE_URL = 'http://localhost:3000/api';

// Load categories from API
async function loadCategories() {
    const loadingElement = document.getElementById('loading');
    const containerElement = document.getElementById('categories-container');
    const errorElement = document.getElementById('error-message');
    
    try {
        const response = await fetch(`${API_BASE_URL}/categories`);
        
        if (!response.ok) {
            throw new Error('Failed to fetch categories');
        }
        
        const categories = await response.json();
        
        // Hide loading
        loadingElement.classList.add('d-none');
        
        // Show categories container
        containerElement.classList.remove('d-none');
        
        // Populate categories
        containerElement.innerHTML = categories.map((category, index) => `
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="category-card" onclick="selectCategory(${index}, '${category.title}')">
                    <div class="category-icon">${category.icon}</div>
                    <div class="category-title">${category.title}</div>
                    <div class="category-description">${category.description}</div>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading categories:', error);
        
        // Hide loading
        loadingElement.classList.add('d-none');
        
        // Show error message
        errorElement.classList.remove('d-none');
        errorElement.textContent = 'Kateqoriyalar yüklənərkən xəta baş verdi. Səhifəni yeniləyib yenidən cəhd edin.';
    }
}

// Handle category selection
function selectCategory(index, title) {
    // For now, just show an alert. In a real app, this would navigate to a complaint form
    alert(`"${title}" kateqoriyası seçildi. Bu kateqoriyada şikayət bildirmək üçün giriş etməlisiniz.`);
    
    // Check if user is logged in
    checkAuthAndRedirect();
}

// Check authentication and redirect if needed
async function checkAuthAndRedirect() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
            credentials: 'include'
        });
        
        if (response.ok) {
            // User is authenticated, could redirect to complaint form
            alert('Şikayət formu tezliklə əlavə ediləcək!');
        } else {
            // User not authenticated, redirect to login
            if (confirm('Şikayət bildirmək üçün giriş etməlisiniz. Giriş səhifəsinə yönləndirilsinmi?')) {
                window.location.href = 'login.html';
            }
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        if (confirm('Şikayət bildirmək üçün giriş etməlisiniz. Giriş səhifəsinə yönləndirilsinmi?')) {
            window.location.href = 'login.html';
        }
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
});
