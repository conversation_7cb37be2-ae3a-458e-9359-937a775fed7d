/* Categories page specific styles */

.categories-section {
  min-height: 70vh;
  background-color: #f8f9fa;
}

.category-card {
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  padding: 30px 20px;
  margin-bottom: 20px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  border-color: #007bff;
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.category-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

/* Loading and error states */
#loading {
  padding: 50px 0;
}

#error-message {
  margin-top: 30px;
}

/* Responsive design */
@media (max-width: 768px) {
  .category-card {
    padding: 20px 15px;
  }
  
  .category-icon {
    font-size: 2.5rem;
  }
  
  .category-title {
    font-size: 1.3rem;
  }
}
