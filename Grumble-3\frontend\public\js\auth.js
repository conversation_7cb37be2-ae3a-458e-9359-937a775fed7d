// Configuration
const API_BASE_URL = 'http://localhost:3000/api';

// Utility functions
function showMessage(elementId, message, isError = false) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.className = `alert ${isError ? 'alert-danger' : 'alert-success'}`;
        element.classList.remove('d-none');
    }
}

function hideMessage(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add('d-none');
    }
}

function showSpinner(spinnerId) {
    const spinner = document.getElementById(spinnerId);
    if (spinner) {
        spinner.classList.remove('d-none');
    }
}

function hideSpinner(spinnerId) {
    const spinner = document.getElementById(spinnerId);
    if (spinner) {
        spinner.classList.add('d-none');
    }
}

// Login form handler
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            hideMessage('error-message');
            hideMessage('success-message');
            showSpinner('login-spinner');
            
            const formData = new FormData(loginForm);
            const data = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('success-message', result.message);
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    showMessage('error-message', result.error, true);
                }
            } catch (error) {
                showMessage('error-message', 'Bağlantı xətası baş verdi', true);
            } finally {
                hideSpinner('login-spinner');
            }
        });
    }
    
    // Signup form handler
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            hideMessage('error-message');
            hideMessage('success-message');
            
            const formData = new FormData(signupForm);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            
            if (password !== confirmPassword) {
                showMessage('error-message', 'Şifrələr uyğun gəlmir', true);
                return;
            }
            
            showSpinner('signup-spinner');
            
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                password: password
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('success-message', result.message);
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else {
                    showMessage('error-message', result.error, true);
                }
            } catch (error) {
                showMessage('error-message', 'Bağlantı xətası baş verdi', true);
            } finally {
                hideSpinner('signup-spinner');
            }
        });
    }
    
    // Forgot password form handler
    const forgotForm = document.getElementById('forgot-password-form');
    if (forgotForm) {
        forgotForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            hideMessage('error-message');
            hideMessage('success-message');
            showSpinner('forgot-spinner');
            
            const formData = new FormData(forgotForm);
            const data = {
                email: formData.get('email')
            };
            
            try {
                // For now, just show a success message since password reset isn't implemented
                setTimeout(() => {
                    showMessage('success-message', 'Şifrə bərpa linki e-mail ünvanınıza göndərildi');
                    hideSpinner('forgot-spinner');
                }, 1000);
            } catch (error) {
                showMessage('error-message', 'Bağlantı xətası baş verdi', true);
                hideSpinner('forgot-spinner');
            }
        });
    }
});
