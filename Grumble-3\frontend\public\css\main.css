/* Main styles for Grumble frontend */

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  min-height: 60vh;
  display: flex;
  align-items: center;
}

/* Complaint Section Styles */
.complaint-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60px 80px;
  background-color: #fff;
  flex-wrap: wrap;
}

.category-count {
  flex: 1;
  min-width: 280px;
  margin-right: 40px;
  text-align: center;
  position: relative;
}

.category-count .badge {
  background-color: #e21b1b;
  color: white;
  padding: 6px 14px;
  border-radius: 10px;
  font-weight: bold;
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.category-count h1 {
  font-size: 120px;
  color: #0b1120;
  margin: 0;
}

.category-count h1 span {
  font-size: 60px;
  vertical-align: top;
}

.category-count p {
  font-size: 16px;
  color: #6c7a89;
  margin-top: 10px;
}

.category-description {
  flex: 2;
  min-width: 300px;
}

.category-description h2 {
  font-size: 36px;
  font-weight: 700;
  color: #0b1120;
  margin-bottom: 10px;
}

.category-description h3 {
  font-size: 20px;
  color: #0c223e;
  margin-bottom: 20px;
}

.category-description p {
  font-size: 16px;
  color: #6c7a89;
  line-height: 1.6;
}

/* Footer Styles */
.footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 40px;
  background-color: #1e2530;
  color: #d1d1d1;
}

.footer-section {
  flex: 1 1 200px;
  margin: 20px;
}

.footer-section h2, .footer-section h3 {
  color: #ffffff;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #bbbbbb;
  text-decoration: none;
}

.footer-section ul li a:hover {
  color: #ffffff;
}

.footer-section .hot {
  color: red;
  font-weight: bold;
}

.social-icons a {
  margin-right: 10px;
  color: #bbbbbb;
  text-decoration: none;
  font-size: 18px;
}

.social-icons a:hover {
  color: #ffffff;
}

.subscribe-form input[type="email"] {
  padding: 8px;
  margin-right: 8px;
  border: none;
  border-radius: 4px;
}

.subscribe-form button {
  padding: 8px 12px;
  background-color: #ff5722;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}

.footer-bottom {
  width: 100%;
  text-align: center;
  margin-top: 40px;
  border-top: 1px solid #444;
  padding-top: 20px;
}

.footer-bottom ul {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
}

.footer-bottom ul li {
  margin: 0 10px;
}

.footer-bottom ul li a {
  color: #bbbbbb;
  text-decoration: none;
}

.footer-bottom ul li a:hover {
  color: #ffffff;
}

.email-link {
  color: #bbbbbb !important;
}

.email-link:hover {
  color: #ffffff !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .complaint-section {
    padding: 40px 20px;
    flex-direction: column;
  }
  
  .category-count {
    margin-right: 0;
    margin-bottom: 30px;
  }
  
  .category-count h1 {
    font-size: 80px;
  }
  
  .footer {
    padding: 20px;
  }
  
  .footer-section {
    margin: 10px 0;
  }
}
