const express = require('express');
const bcrypt = require('bcrypt');
const passport = require('passport');
const User = require('../models/User');
const router = express.Router();

// POST: Signup
router.post('/signup', async (req, res) => {
    const { name, email, password } = req.body;
    
    try {
        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(400).json({ error: 'İstifadəçi artıq mövcuddur' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = new User({ name, email, password: hashedPassword });
        await newUser.save();
        
        res.status(201).json({ message: 'Uğurla qeydiyyatdan keçdiniz!' });
    } catch (error) {
        res.status(500).json({ error: 'Xəta baş verdi: ' + error.message });
    }
});

// POST: Login
router.post('/login', async (req, res) => {
    const { email, password } = req.body;
    
    try {
        const user = await User.findOne({ email });

        if (user && user.password && await bcrypt.compare(password, user.password)) {
            req.session.userId = user._id;
            res.json({ 
                message: 'Uğurlu giriş!', 
                user: { 
                    id: user._id, 
                    name: user.name, 
                    email: user.email,
                    avatar: user.avatar 
                } 
            });
        } else {
            res.status(401).json({ error: 'Email və ya şifrə yanlışdır ❌' });
        }
    } catch (error) {
        res.status(500).json({ error: 'Xəta baş verdi: ' + error.message });
    }
});

// GET: Google OAuth
router.get('/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

// GET: Google OAuth Callback
router.get('/google/callback', 
    passport.authenticate('google', { failureRedirect: process.env.FRONTEND_URL + '/login' }),
    (req, res) => {
        // Successful authentication, redirect to frontend dashboard
        res.redirect(process.env.FRONTEND_URL + '/dashboard');
    }
);

// POST: Logout
router.post('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Session destroy error:', err);
            return res.status(500).json({ error: 'Çıxış zamanı xəta baş verdi' });
        }
        res.json({ message: 'Uğurla çıxış edildi' });
    });
});

// GET: Check authentication status
router.get('/me', async (req, res) => {
    if (!req.session.userId && !req.user) {
        return res.status(401).json({ error: 'Giriş edilməyib' });
    }

    try {
        let user = req.user;
        if (!user && req.session.userId) {
            user = await User.findById(req.session.userId);
        }

        if (!user) {
            return res.status(401).json({ error: 'İstifadəçi tapılmadı' });
        }

        res.json({ 
            user: { 
                id: user._id, 
                name: user.name, 
                email: user.email,
                avatar: user.avatar,
                provider: user.provider
            } 
        });
    } catch (error) {
        res.status(500).json({ error: 'Xəta baş verdi: ' + error.message });
    }
});

module.exports = router;
