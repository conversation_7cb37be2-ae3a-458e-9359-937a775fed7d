# Grumble - Şikayət Platforması

Grumble, şirkətlərlə bağlı problemlərinizi paylaşabileceğiniz və həll yolları tapa biləcəyiniz bir platformadır.

## Proje Strukturu

Bu proje frontend və backend olmaqla iki ayrı hissədən ibarətdir:

```
Grumble-3/
├── backend/          # Node.js/Express API server
│   ├── auth/         # Authentication strategies
│   ├── models/       # Database models
│   ├── routes/       # API routes
│   ├── server.js     # Main server file
│   └── package.json  # Backend dependencies
├── frontend/         # React frontend application
│   ├── public/       # Static HTML files
│   ├── src/          # React components
│   └── package.json  # Frontend dependencies
└── README.md
```

## Xüsusiyyətlər

### Backend
- **Authentication**: Email/password və Google OAuth
- **API Endpoints**: RESTful API
- **Database**: MongoDB with Mongoose
- **Session Management**: Express sessions
- **CORS**: Cross-origin resource sharing

### Frontend
- **Static HTML**: Performans üçün EJS-dən HTML-ə çevrilmiş
- **React Components**: Kateqoriyalar üçün React komponentləri
- **Responsive Design**: Bootstrap 5 ilə responsive dizayn
- **Modern JavaScript**: ES6+ xüsusiyyətləri

## Quraşdırma

### Tələblər
- Node.js (v14 və ya daha yeni)
- MongoDB
- npm və ya yarn

### Backend Quraşdırması

1. Backend qovluğuna keçin:
```bash
cd backend
```

2. Dependencies quraşdırın:
```bash
npm install
```

3. Environment variables yaradın:
```bash
cp .env.example .env
```

4. `.env` faylını redaktə edin və lazımi məlumatları əlavə edin:
```env
MONGODB_URI=mongodb://127.0.0.1:27017/grumbleDB
GOOGLE_CLIENT_SECRET=your_google_client_secret
SESSION_SECRET=your_session_secret
PORT=3000
FRONTEND_URL=http://localhost:3001
```

5. MongoDB-nin işlədiyindən əmin olun və serveri başladın:
```bash
npm start
```

Backend server http://localhost:3000 ünvanında işləyəcək.

### Frontend Quraşdırması

1. Frontend qovluğuna keçin:
```bash
cd frontend
```

2. Dependencies quraşdırın:
```bash
npm install
```

3. Development server başladın:
```bash
npm start
```

React development server http://localhost:3001 ünvanında işləyəcək.

### Production Build

Frontend üçün production build yaratmaq:
```bash
cd frontend
npm run build
npm run serve
```

## API Endpoints

### Authentication
- `POST /api/auth/signup` - Qeydiyyat
- `POST /api/auth/login` - Giriş
- `GET /api/auth/google` - Google OAuth
- `GET /api/auth/google/callback` - Google OAuth callback
- `POST /api/auth/logout` - Çıxış
- `GET /api/auth/me` - İstifadəçi məlumatları

### Categories
- `GET /api/categories` - Bütün kateqoriyalar
- `GET /api/categories/:id` - Spesifik kateqoriya

### Health Check
- `GET /api/health` - Server status

## Fayllar

### Static HTML Files (Frontend/public/)
- `index.html` - Ana səhifə
- `login.html` - Giriş səhifəsi
- `signup.html` - Qeydiyyat səhifəsi
- `dashboard.html` - İstifadəçi paneli
- `categories.html` - Kateqoriyalar səhifəsi
- `forgot-password.html` - Şifrə bərpası

### React Components (Frontend/src/)
- `App.js` - Əsas React komponenti
- `index.js` - React entry point

## Performans Təkmilləşdirmələri

1. **EJS-dən HTML-ə çevrilmə**: Server-side rendering əvəzinə static HTML faylları
2. **API separation**: Frontend və backend ayrı portlarda
3. **Modern JavaScript**: Vanilla JS və React kombinasiyası
4. **Responsive CSS**: Mobile-first approach
5. **CDN Resources**: Bootstrap və Font Awesome CDN-dən

## Təhlükəsizlik

- Password hashing (bcrypt)
- Session management
- CORS configuration
- Environment variables
- Input validation

## Gələcək Təkmilləşdirmələr

- [ ] Şifrə bərpası funksionallığı
- [ ] Email verification
- [ ] Şikayət formu
- [ ] Admin paneli
- [ ] Real-time notifications
- [ ] File upload
- [ ] Search functionality

## Lisenziya

Bu proje MIT lisenziyası altındadır.
