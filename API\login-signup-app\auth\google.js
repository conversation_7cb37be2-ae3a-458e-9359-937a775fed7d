const GoogleStrategy = require('passport-google-oauth20').Strategy;
const User = require('../models/User');

module.exports = function(passport) {
    passport.use(new GoogleStrategy({
        clientID: '929920815774-e8rmim5ksg6332al2bnlseqvmbh2atud.apps.googleusercontent.com',
        clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'YOUR_GOOGLE_CLIENT_SECRET', // You'll need to set this
        callbackURL: '/auth/google/callback'
    },
    async (accessToken, refreshToken, profile, done) => {
        try {
            // Check if user already exists with this Google ID
            let user = await User.findOne({ googleId: profile.id });

            if (user) {
                // User exists, return the user
                return done(null, user);
            }

            // Check if user exists with the same email
            user = await User.findOne({ email: profile.emails[0].value });

            if (user) {
                // User exists with same email, link Google account
                user.googleId = profile.id;
                user.provider = 'google';
                user.avatar = profile.photos[0].value;
                await user.save();
                return done(null, user);
            }

            // Create new user
            user = new User({
                googleId: profile.id,
                name: profile.displayName,
                email: profile.emails[0].value,
                provider: 'google',
                avatar: profile.photos[0].value
            });

            await user.save();
            return done(null, user);

        } catch (error) {
            console.error('Google OAuth error:', error);
            return done(error, null);
        }
    }));
};