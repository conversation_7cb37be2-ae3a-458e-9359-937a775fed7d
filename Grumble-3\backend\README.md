# Grumble Backend API

Node.js/Express backend server for the Grumble complaint platform.

## Features

- **Authentication**: Local (email/password) and Google OAuth
- **User Management**: MongoDB with Mongoose
- **Session Management**: Express sessions with secure configuration
- **API Routes**: RESTful API endpoints
- **CORS**: Configured for frontend communication
- **Security**: Password hashing, input validation, environment variables

## Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Configure your `.env` file:
```env
MONGODB_URI=mongodb://127.0.0.1:27017/grumbleDB
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
SESSION_SECRET=your_session_secret_here
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3001
```

4. Start MongoDB service

5. Run the server:
```bash
npm start
```

For development with auto-restart:
```bash
npm run dev
```

## API Endpoints

### Authentication Routes (`/api/auth`)

- `POST /signup` - User registration
- `POST /login` - User login
- `GET /google` - Initiate Google OAuth
- `GET /google/callback` - Google OAuth callback
- `POST /logout` - User logout
- `GET /me` - Get current user info

### Categories Routes (`/api/categories`)

- `GET /` - Get all categories
- `GET /:id` - Get specific category

### Health Check

- `GET /api/health` - Server health status

## Database Models

### User Model
```javascript
{
  name: String,
  email: String (unique),
  password: String (optional for OAuth),
  googleId: String (unique, sparse),
  provider: String (default: 'local'),
  avatar: String,
  timestamps: true
}
```

## Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:3000/auth/google/callback`
6. Copy Client Secret to `.env` file

## Security Features

- Password hashing with bcrypt
- Secure session configuration
- CORS protection
- Environment variable configuration
- Input validation and error handling

## Development

The server runs on port 3000 by default. Make sure MongoDB is running before starting the server.

For production deployment, set `NODE_ENV=production` and configure appropriate security settings.
