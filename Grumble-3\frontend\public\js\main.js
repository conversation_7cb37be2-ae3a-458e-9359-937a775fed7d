// Main JavaScript for index page
const API_BASE_URL = 'http://localhost:3000/api';

// Load complaint section content
function loadComplaintSection() {
    const complaintSection = document.getElementById('complaint-section');
    if (complaintSection) {
        complaintSection.innerHTML = `
            <section class="complaint-section">
                <div class="category-count">
                    <div class="badge">KATEQORİYA</div>
                    <h1>50<span>+</span></h1>
                    <p>← Platformamız bütün sahələri əhatə edir.</p>
                </div>

                <div class="category-description">
                    <h2>Şikayət kateqoriyaları.</h2>
                    <h3>Geniş və əhatəli kateqoriyalar.</h3>
                    <p>
                        Platformamızın geniş kateqoriya kitabxanası sizə istənilən sahədə
                        şikayət bildirmək imkanı verir və problemlərinizi düzgün yerə yönləndirir.
                    </p>
                </div>
            </section>
        `;
    }
}

// Load footer content
function loadFooterSection() {
    const footerSection = document.getElementById('footer-section');
    if (footerSection) {
        footerSection.innerHTML = `
            <div class="footer">
                <div class="footer-section">
                    <h2>Grumble</h2>
                    <p>Şirkətlərlə bağlı problemlərinizi paylaşın və həllini tapın.<br>
                       Milyonlarla istifadəçi təcrübəsindən faydalanın.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Şirkət</h3>
                    <ul>
                        <li><a href="#">Biz kimik</a></li>
                        <li><a href="#">Xidmətlərimiz <span class="hot">HOT</span></a></li>
                        <li><a href="#">Müştərilərimiz</a></li>
                        <li><a href="#">Əlaqə</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Xidmətlərimiz</h3>
                    <ul>
                        <li><a href="#">Şikayət bildirimi</a></li>
                        <li><a href="#">Şirkət monitorinqi</a></li>
                        <li><a href="#">Həll strategiyası</a></li>
                        <li><a href="#">Müştəri dəstəyi</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Kömək lazımdır?</h3>
                    <p><strong>Birbaşa zəng edin?</strong><br>(012) 567 89 01</p>
                    <p><strong>Dəstək lazımdır?</strong><br><a href="mailto:<EMAIL>" class="email-link"><EMAIL></a></p>
                </div>

                <div class="footer-section">
                    <h3>Xəbər bülletenimizə abunə olun</h3>
                    <p>Ən son xəbərləri əldə etmək üçün xəbər bülletenimizə abunə olun.</p>
                    <form class="subscribe-form">
                        <input type="email" placeholder="E-mail ünvanınızı daxil edin" required>
                        <button type="submit">📩</button>
                    </form>
                </div>

                <div class="footer-bottom">
                    <p>© 2025 Grumble tərəfindən hazırlanmışdır</p>
                    <ul>
                        <li><a href="#">Məxfilik siyasəti</a></li>
                        <li><a href="#">İstifadə şərtləri</a></li>
                        <li><a href="#">Müəllif hüquqları</a></li>
                    </ul>
                </div>
            </div>
        `;
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadComplaintSection();
    loadFooterSection();
    
    // Handle newsletter subscription
    const subscribeForm = document.querySelector('.subscribe-form');
    if (subscribeForm) {
        subscribeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                alert('Təşəkkür edirik! Xəbər bülletenimizə abunə oldunuz.');
                this.reset();
            }
        });
    }
});
