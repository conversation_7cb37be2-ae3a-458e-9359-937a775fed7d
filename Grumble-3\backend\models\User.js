const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: String,
    email: { type: String, unique: true },
    password: { type: String, required: false }, // Optional for OAuth users
    googleId: { type: String, unique: true, sparse: true }, // Google OAuth ID
    provider: { type: String, default: 'local' }, // 'local' or 'google'
    avatar: String // Profile picture URL from Google
}, {
    timestamps: true // Add createdAt and updatedAt fields
});

module.exports = mongoose.model('User', userSchema);
